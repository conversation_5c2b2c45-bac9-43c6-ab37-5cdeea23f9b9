import {
  GET_BUDGET_CALCULATION_BY_ID_REQUEST,
  GET_BUDGET_CALCULATION_BY_ID_SUCCESS,
  GET_BUDGET_CALCULATION_BY_ID_FAILURE,
  UPDATE_BUDGET_CALCULATION_PROGRESS,
  CLEAR_BUDGET_CALCULATION_BY_ID,
} from './actionTypes';

export const getBudgetCalculationByIdRequest = (budgetId) => ({
  type: GET_BUDGET_CALCULATION_BY_ID_REQUEST,
  budgetId,
});

export const getBudgetCalculationByIdSuccess = (budgetId, data) => ({
  type: GET_BUDGET_CALCULATION_BY_ID_SUCCESS,
  budgetId,
  data,
});

export const getBudgetCalculationByIdFailure = (budgetId, error) => ({
  type: GET_BUDGET_CALCULATION_BY_ID_FAILURE,
  budgetId,
  error,
});

export const updateBudgetCalculationProgress = (budgetId, progress) => ({
  type: UPDATE_BUDGET_CALCULATION_PROGRESS,
  budgetId,
  progress,
});

export const clearBudgetCalculationById = (budgetId) => ({
  type: CLEAR_BUDGET_CALCULATION_BY_ID,
  budgetId,
});

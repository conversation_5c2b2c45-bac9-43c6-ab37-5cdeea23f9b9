import {
  getBudgetCalculationByIdRequest,
  getBudgetCalculationByIdSuccess,
  getBudgetCalculationByIdFailure,
} from './actionsCreators';
import getLastBudgetCalculation from '../GetLastBudgetCalculation/api.service';

const getBudgetCalculationByIdAction = (controller, budgetId) => async (dispatch) => {
  try {
    const { signal } = controller;

    dispatch(getBudgetCalculationByIdRequest(budgetId));

    const { data } = await getLastBudgetCalculation(signal, budgetId);

    dispatch(getBudgetCalculationByIdSuccess(budgetId, data));
  } catch (error) {
    dispatch(getBudgetCalculationByIdFailure(budgetId, error));
    throw error;
  }
};

export default getBudgetCalculationByIdAction;

import {
  GET_BUDGET_CALCULATION_BY_ID_REQUEST,
  GET_BUDGET_CALCULATION_BY_ID_SUCCESS,
  GET_BUDGET_CALCULATION_BY_ID_FAILURE,
  UPDATE_BUDGET_CALCULATION_PROGRESS,
  CLEAR_BUDGET_CALCULATION_BY_ID,
} from './actionTypes';

const initialState = {
  byBudgetId: {},
  loading: {},
  errors: {},
};

const budgetCalculationsByBudgetIdReducer = (state = initialState, action) => {
  const {
    type, budgetId, data, error, progress,
  } = action;

  switch (type) {
    case GET_BUDGET_CALCULATION_BY_ID_REQUEST:
      return {
        ...state,
        loading: {
          ...state.loading,
          [budgetId]: true,
        },
        errors: {
          ...state.errors,
          [budgetId]: null,
        },
      };

    case GET_BUDGET_CALCULATION_BY_ID_SUCCESS:
      return {
        ...state,
        byBudgetId: {
          ...state.byBudgetId,
          [budgetId]: {
            ...state.byBudgetId[budgetId],
            data,
          },
        },
        loading: {
          ...state.loading,
          [budgetId]: false,
        },
        errors: {
          ...state.errors,
          [budgetId]: null,
        },
      };

    case GET_BUDGET_CALCULATION_BY_ID_FAILURE:
      return {
        ...state,
        loading: {
          ...state.loading,
          [budgetId]: false,
        },
        errors: {
          ...state.errors,
          [budgetId]: error,
        },
      };

    case UPDATE_BUDGET_CALCULATION_PROGRESS:
      return {
        ...state,
        byBudgetId: {
          ...state.byBudgetId,
          [budgetId]: {
            ...state.byBudgetId[budgetId],
            progress,
          },
        },
      };

    case CLEAR_BUDGET_CALCULATION_BY_ID: {
      const newByBudgetId = { ...state.byBudgetId };
      const newLoading = { ...state.loading };
      const newErrors = { ...state.errors };

      delete newByBudgetId[budgetId];
      delete newLoading[budgetId];
      delete newErrors[budgetId];

      return {
        ...state,
        byBudgetId: newByBudgetId,
        loading: newLoading,
        errors: newErrors,
      };
    }

    default:
      return state;
  }
};

export default budgetCalculationsByBudgetIdReducer;

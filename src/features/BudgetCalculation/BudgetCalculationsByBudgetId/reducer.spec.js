import budgetCalculationsByBudgetIdReducer from './reducer';
import {
  GET_BUDGET_CALCULATION_BY_ID_REQUEST,
  GET_BUDGET_CALCULATION_BY_ID_SUCCESS,
  GET_BUDGET_CALCULATION_BY_ID_FAILURE,
  UPDATE_BUDGET_CALCULATION_PROGRESS,
  CLEAR_BUDGET_CALCULATION_BY_ID,
} from './actionTypes';

describe('budgetCalculationsByBudgetIdReducer', () => {
  const initialState = {
    byBudgetId: {},
    loading: {},
    errors: {},
  };

  const budgetId = 123;
  const mockData = { status: 'CALCULATED', created_at: '2023-01-01' };
  const mockError = { message: 'Error occurred' };
  const mockProgress = { isBudgetCalculationRunning: true };

  test('should return initial state', () => {
    expect(budgetCalculationsByBudgetIdReducer(undefined, {})).toEqual(initialState);
  });

  test('should handle GET_BUDGET_CALCULATION_BY_ID_REQUEST', () => {
    const action = {
      type: GET_BUDGET_CALCULATION_BY_ID_REQUEST,
      budgetId,
    };

    const expectedState = {
      ...initialState,
      loading: { [budgetId]: true },
      errors: { [budgetId]: null },
    };

    expect(budgetCalculationsByBudgetIdReducer(initialState, action)).toEqual(expectedState);
  });

  test('should handle GET_BUDGET_CALCULATION_BY_ID_SUCCESS', () => {
    const action = {
      type: GET_BUDGET_CALCULATION_BY_ID_SUCCESS,
      budgetId,
      data: mockData,
    };

    const expectedState = {
      ...initialState,
      byBudgetId: {
        [budgetId]: {
          data: mockData,
        },
      },
      loading: { [budgetId]: false },
      errors: { [budgetId]: null },
    };

    expect(budgetCalculationsByBudgetIdReducer(initialState, action)).toEqual(expectedState);
  });

  test('should handle GET_BUDGET_CALCULATION_BY_ID_FAILURE', () => {
    const action = {
      type: GET_BUDGET_CALCULATION_BY_ID_FAILURE,
      budgetId,
      error: mockError,
    };

    const expectedState = {
      ...initialState,
      loading: { [budgetId]: false },
      errors: { [budgetId]: mockError },
    };

    expect(budgetCalculationsByBudgetIdReducer(initialState, action)).toEqual(expectedState);
  });

  test('should handle UPDATE_BUDGET_CALCULATION_PROGRESS', () => {
    const stateWithExistingBudget = {
      ...initialState,
      byBudgetId: {
        [budgetId]: {
          data: mockData,
        },
      },
    };

    const action = {
      type: UPDATE_BUDGET_CALCULATION_PROGRESS,
      budgetId,
      progress: mockProgress,
    };

    const expectedState = {
      ...stateWithExistingBudget,
      byBudgetId: {
        [budgetId]: {
          data: mockData,
          progress: mockProgress,
        },
      },
    };

    expect(budgetCalculationsByBudgetIdReducer(stateWithExistingBudget, action)).toEqual(expectedState);
  });

  test('should handle CLEAR_BUDGET_CALCULATION_BY_ID', () => {
    const stateWithBudget = {
      byBudgetId: { [budgetId]: { data: mockData } },
      loading: { [budgetId]: false },
      errors: { [budgetId]: null },
    };

    const action = {
      type: CLEAR_BUDGET_CALCULATION_BY_ID,
      budgetId,
    };

    expect(budgetCalculationsByBudgetIdReducer(stateWithBudget, action)).toEqual(initialState);
  });
});

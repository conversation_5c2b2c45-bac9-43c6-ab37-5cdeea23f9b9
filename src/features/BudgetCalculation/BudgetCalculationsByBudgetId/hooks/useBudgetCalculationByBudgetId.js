import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';
import { HTTPService } from 'core/services';
import getBudgetCalculationByIdAction from '../actions';
import { clearBudgetCalculationById } from '../actionsCreators';
import useBudgetCalculationStatusByBudgetId from './useBudgetCalculationStatusByBudgetId';

const controllers = {};

const useBudgetCalculationByBudgetId = (budgetId) => {
  const dispatch = useDispatch();

  const budgetCalculationState = useSelector((state) => (
    state.budgetCalculationsByBudgetId?.byBudgetId?.[budgetId] || {}
  ));

  const isLoading = useSelector((state) => (
    state.budgetCalculationsByBudgetId?.loading?.[budgetId] || false
  ));

  const error = useSelector((state) => (
    state.budgetCalculationsByBudgetId?.errors?.[budgetId] || null
  ));

  const { isCalculationStateIsRunning } = useBudgetCalculationStatusByBudgetId(budgetId);

  const cancelRequest = useCallback(() => {
    if (controllers[budgetId]) {
      HTTPService.cancelRequest(controllers[budgetId]);
      delete controllers[budgetId];
    }
  }, [budgetId]);

  const getBudgetCalculation = useCallback(async () => {
    cancelRequest();

    controllers[budgetId] = HTTPService.getController();

    try {
      await dispatch(getBudgetCalculationByIdAction(controllers[budgetId], budgetId));
    } catch (e) {
      // Error handled by reducer
    }
  }, [budgetId, dispatch, cancelRequest]);

  const clearBudgetCalculation = useCallback(() => {
    cancelRequest();
    dispatch(clearBudgetCalculationById(budgetId));
  }, [budgetId, dispatch, cancelRequest]);

  useEffect(() => {
    getBudgetCalculation();

    return () => {
      cancelRequest();
    };
  }, [getBudgetCalculation, cancelRequest]);

  return {
    budgetCalculationData: budgetCalculationState.data || {},
    progress: budgetCalculationState.progress || {},
    isLoading,
    error,
    isCalculationStateIsRunning,
    getBudgetCalculation,
    clearBudgetCalculation,
  };
};

export default useBudgetCalculationByBudgetId;

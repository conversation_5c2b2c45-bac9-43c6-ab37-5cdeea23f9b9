import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from '@reduxjs/toolkit';

import useBudgetCalculationByBudgetId from './useBudgetCalculationByBudgetId';
import budgetCalculationsByBudgetIdReducer from '../reducer';

// Mock HTTPService
jest.mock('core/services', () => ({
  HTTPService: {
    getController: jest.fn(() => ({ signal: {} })),
    cancelRequest: jest.fn(),
  },
}));

describe('useBudgetCalculationByBudgetId', () => {
  const budgetId = 123;

  const createStore = (initialState = {}) => {
    return configureStore({
      reducer: combineReducers({
        budgetCalculationsByBudgetId: budgetCalculationsByBudgetIdReducer,
      }),
      preloadedState: {
        budgetCalculationsByBudgetId: {
          byBudgetId: {},
          loading: {},
          errors: {},
          ...initialState,
        },
      },
      middleware: (getDefaultMiddleware) => getDefaultMiddleware({
        immutableCheck: false,
        serializableCheck: false,
      }),
    });
  };

  const wrapper = ({ children, store }) => (
    <Provider store={store}>{children}</Provider>
  );

  test('should return initial state when no data exists', () => {
    const store = createStore();
    const { result } = renderHook(() => useBudgetCalculationByBudgetId(budgetId), {
      wrapper: ({ children }) => wrapper({ children, store }),
    });

    expect(result.current.budgetCalculationData).toEqual({});
    expect(result.current.progress).toEqual({});
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  test('should return budget calculation data when it exists', () => {
    const mockData = { status: 'CALCULATED', created_at: '2023-01-01' };
    const mockProgress = { isBudgetCalculationRunning: false };
    
    const store = createStore({
      byBudgetId: {
        [budgetId]: {
          data: mockData,
          progress: mockProgress,
        },
      },
    });

    const { result } = renderHook(() => useBudgetCalculationByBudgetId(budgetId), {
      wrapper: ({ children }) => wrapper({ children, store }),
    });

    expect(result.current.budgetCalculationData).toEqual(mockData);
    expect(result.current.progress).toEqual(mockProgress);
  });

  test('should return loading state', () => {
    const store = createStore({
      loading: { [budgetId]: true },
    });

    const { result } = renderHook(() => useBudgetCalculationByBudgetId(budgetId), {
      wrapper: ({ children }) => wrapper({ children, store }),
    });

    expect(result.current.isLoading).toBe(true);
  });

  test('should return error state', () => {
    const mockError = { message: 'Error occurred' };
    const store = createStore({
      errors: { [budgetId]: mockError },
    });

    const { result } = renderHook(() => useBudgetCalculationByBudgetId(budgetId), {
      wrapper: ({ children }) => wrapper({ children, store }),
    });

    expect(result.current.error).toEqual(mockError);
  });
});

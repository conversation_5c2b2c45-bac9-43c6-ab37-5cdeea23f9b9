import { useSelector } from 'react-redux';
import calculationStatuses from 'features/BudgetCalculation/constants';

export const calculationStates = {
  isNotRunning: 'isNotRunning',
  isRunning: 'isRunning',
  wasFailed: 'wasFailed',
};

const useBudgetCalculationStatusByBudgetId = (budgetId) => {
  const budgetCalculationData = useSelector((state) => 
    state.budgetCalculationsByBudgetId?.byBudgetId?.[budgetId]?.data || {}
  );

  const {
    notCalculated,
    failed,
    waitingForStart,
    started,
    forecastRulesApplication,
    agreementsApplication,
    isFinishing,
    externalCalculationResultsApplication,
    calculated,
    historicalTrafficSynchronization,
  } = calculationStatuses;

  let currentCalculationState;
  let isCalculationStateIsRunning = false;

  switch (budgetCalculationData.status) {
    case notCalculated.value:
    case calculated.value:
      currentCalculationState = calculationStates.isNotRunning;
      break;
    case waitingForStart.value:
    case historicalTrafficSynchronization.value:
    case agreementsApplication.value:
    case forecastRulesApplication.value:
    case isFinishing.value:
    case externalCalculationResultsApplication.value:
    case started.value:
      currentCalculationState = calculationStates.isRunning;
      isCalculationStateIsRunning = true;
      break;
    case failed.value:
      currentCalculationState = calculationStates.wasFailed;
      break;
    default:
      currentCalculationState = calculationStates.isNotRunning;
  }

  return {
    calculationStates,
    currentCalculationState,
    isCalculationStateIsRunning,
  };
};

export default useBudgetCalculationStatusByBudgetId;

import React from 'react';
import PropTypes from 'prop-types';

import { AiOutlinePlayCircle } from 'react-icons/ai';
import ActionButtonWithTooltip from '@nv2/nv2-pkg-js-shared-components/lib/ActionButtonWithTooltip';

import { useAppContext } from 'AppContextProvider';
import { getFormattedDate } from 'core/utilities/getFormattedDate';
import CustomCircularProgress from 'shared/CustomCircularProgress';
import useBudgetCalculationByBudgetId from '../BudgetCalculationsByBudgetId/hooks/useBudgetCalculationByBudgetId';
import {
  getTooltipTextWhenCalculationWasFailed,
  getTooltipTextWhenCalculationIsNotRunning,
  getTooltipTextWhenCalculationIsRunning,
  getDefaultTooltipText,
} from '../BudgetCalculationButton/utilities';
import './BudgetCalculationButtonForBudgetList.scss';

const BudgetCalculationButtonForBudgetList = ({ budgetId, onClick, className }) => {
  const { primaryColor } = useAppContext();
  const {
    budgetCalculationData,
    isCalculationStateIsRunning,
    progress,
  } = useBudgetCalculationByBudgetId(budgetId);

  const createdAtDataFromLastCalculationData = getFormattedDate(
    budgetCalculationData.created_at,
  );
  const finishedAtDateFromLastCalculationData = getFormattedDate(
    budgetCalculationData.finished_at,
  );

  const isCalculationRunning = isCalculationStateIsRunning || progress?.isBudgetCalculationRunning;
  const isBudgetCalculationFailed = progress?.isBudgetCalculationFailed;

  const getTooltipText = () => {
    if (isBudgetCalculationFailed) {
      return getTooltipTextWhenCalculationWasFailed(
        createdAtDataFromLastCalculationData,
        false,
      );
    }

    if (isCalculationRunning) {
      return getTooltipTextWhenCalculationIsRunning(
        createdAtDataFromLastCalculationData,
      );
    }

    if (budgetCalculationData.status) {
      return getTooltipTextWhenCalculationIsNotRunning(
        createdAtDataFromLastCalculationData,
        finishedAtDateFromLastCalculationData,
        false,
      );
    }

    return getDefaultTooltipText();
  };

  const onOpenModal = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onClick();
  };

  return (
    <ActionButtonWithTooltip
      title={getTooltipText()}
      className={`budget-calculation-button-for-list ${className}`}
      action={onOpenModal}
      dataTestid="budget-calculation-button-for-list"
      icon={isCalculationRunning
        ? <CustomCircularProgress />
        : <AiOutlinePlayCircle size={19} color={primaryColor} />}
      primaryColor={primaryColor}
      placement="left"
    />
  );
};

BudgetCalculationButtonForBudgetList.propTypes = {
  budgetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onClick: PropTypes.func.isRequired,
  className: PropTypes.string,
};

BudgetCalculationButtonForBudgetList.defaultProps = {
  className: '',
};

export default BudgetCalculationButtonForBudgetList;

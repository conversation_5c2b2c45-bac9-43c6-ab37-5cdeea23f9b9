import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, combineReducers } from '@reduxjs/toolkit';

import { AppContextProvider } from 'AppContextProvider';
import BudgetCalculationButtonForBudgetList from './BudgetCalculationButtonForBudgetList';
import budgetCalculationsByBudgetIdReducer from '../BudgetCalculationsByBudgetId/reducer';

describe('BudgetCalculationButtonForBudgetList', () => {
  const mockOnClick = jest.fn();
  const budgetId = 123;

  const defaultTestReducer = combineReducers({
    budgetCalculationsByBudgetId: budgetCalculationsByBudgetIdReducer,
  });

  const getStore = (testReducer = defaultTestReducer) => configureStore({
    reducer: testReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }),
  });

  const renderComponent = (store = getStore()) => render(
    <Provider store={store}>
      <AppContextProvider>
        <BudgetCalculationButtonForBudgetList
          budgetId={budgetId}
          onClick={mockOnClick}
        />
      </AppContextProvider>
    </Provider>,
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render budget calculation button', () => {
    renderComponent();

    const button = screen.getByTestId('button-with-tooltip-icon');
    expect(button).toBeInTheDocument();
  });

  test('should call onClick when button is clicked', () => {
    renderComponent();

    const button = screen.getByTestId('button-with-tooltip-icon');
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  test('should show circular progress when calculation is running', () => {
    const storeWithRunningCalculation = configureStore({
      reducer: combineReducers({
        budgetCalculationsByBudgetId: () => ({
          byBudgetId: {
            [budgetId]: {
              data: { status: 'STARTED' },
              progress: { isBudgetCalculationRunning: true },
            },
          },
        }),
      }),
      middleware: (getDefaultMiddleware) => getDefaultMiddleware({
        immutableCheck: false,
        serializableCheck: false,
      }),
    });

    renderComponent(storeWithRunningCalculation);

    // Should show loading indicator when calculation is running
    const button = screen.getByTestId('button-with-tooltip-icon');
    expect(button).toBeInTheDocument();
  });

  test('should prevent event propagation when clicked', () => {
    renderComponent();

    const button = screen.getByTestId('button-with-tooltip-icon');
    const mockEvent = {
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
    };

    fireEvent.click(button, mockEvent);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });
});

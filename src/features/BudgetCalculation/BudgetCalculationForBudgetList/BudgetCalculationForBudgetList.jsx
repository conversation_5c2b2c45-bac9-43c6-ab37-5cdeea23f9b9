import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import BudgetCalculationContainer from 'features/BudgetCalculation';

const BudgetCalculationForBudgetList = ({
  budgetId,
  triggerModal,
}) => {
  const [triggerBudgetCalculationModal, setTriggerBudgetCalculationModal] = useState(0);

  useEffect(() => {
    if (triggerModal > 0) {
      setTriggerBudgetCalculationModal(triggerModal);
    }
  }, [triggerModal]);

  return (
    <BudgetCalculationContainer
      budgetId={budgetId}
      triggerBudgetCalculationModal={triggerBudgetCalculationModal}
      setTriggerBudgetCalculationModal={setTriggerBudgetCalculationModal}
    />
  );
};

BudgetCalculationForBudgetList.propTypes = {
  budgetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  triggerModal: PropTypes.number,
};

BudgetCalculationForBudgetList.defaultProps = {
  triggerModal: 0,
};

export default BudgetCalculationForBudgetList;

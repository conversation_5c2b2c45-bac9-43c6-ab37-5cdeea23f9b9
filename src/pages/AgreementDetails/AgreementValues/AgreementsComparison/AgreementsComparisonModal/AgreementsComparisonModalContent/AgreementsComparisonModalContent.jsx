import { TextField, Box } from '@mui/material';
import { GrDown, GrUp } from 'react-icons/gr';
import PropTypes from 'prop-types';
import useAgreementsForActionsFilters from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/hooks/useAgreementsForActionsFilters';
import useGetAgreementsForActionsFilters from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/hooks/useGetAgreementsForActionsFilters';
import { agreementsTableCompareConfig } from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/configs';
import BudgetSelection from 'pages/BudgetDetails/BudgetItems/shared/ModalToCopyBudgetComponentsFromAnotherBudget/BudgetSelection';
import React, { useState, useEffect } from 'react';
import PerfectScrollbar from 'react-perfect-scrollbar';
import { useSelector } from 'react-redux';

import Autocomplete from 'shared/Autocomplete';
import { calculationStatuses } from 'shared/CalculationStatus/constants';
import { FilterActionButtons } from 'shared/TableFilters';
import AgreementsTransferTable from 'shared/AgreementsTransferTable/AgreementsTransferTable';
import ActionButtonWithTooltip from '@nv2/nv2-pkg-js-shared-components/lib/ActionButtonWithTooltip';
import AgreementFilters from '../AgreementFilters';
import { AgreementsTableProvider } from '../AgreementTable/AgreementTableProvider';
import { agreementsComparisonModalActionsModifier } from '../constants';

import getIsButtonConfirmDisabled from '../utilities';

import './AgreementsComparisonModalContent.scss';

const AgreementsComparisonModalContent = ({
  resetAgreementsComparison,
  confirmComparison,
  agreementsComparisonModalData,
  setAgreementsComparisonModalData,
  agreementMarkersColors,
  removeColorFromAgreementMarkersColors,
  addColorToAgreementMarkersColors,
  currentAgreementId,
}) => {
  const [topSectionSelected, setTopSectionSelected] = useState([]);
  const [bottomSectionSelected, setBottomSectionSelected] = useState([]);
  const initialBudgetValue = useSelector((state) => state.budgets.data?.[0]?.id);
  const [budgetValue, setBudgetValue] = useState(initialBudgetValue);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const availableAgreements = useSelector(
    (state) => state.agreementsForModalAction.data?.results,
  ) || [];
  const budgets = useSelector((state) => state.budgets.data) || [];
  const isBudgetsLoading = useSelector((state) => state.budgets.isLoading);
  const isAgreementsLoading = useSelector((state) => state.agreementsForModalAction.isLoading);
  const comparisonAgreementIds = agreementsComparisonModalData
    .map((a) => a.value?.id).filter(Boolean);

  const isInitialLoading = !budgetValue || isBudgetsLoading
    || (budgetValue && isAgreementsLoading && !availableAgreements.length);

  const {
    agreementsFilters,
    setAgreementsFilters,
    initialAgreementsFilters,
  } = useAgreementsForActionsFilters(null, ['APPLIED']);

  const isFiltersLoading = useSelector((state) => state.agreementsForActionsFilters.isLoading);
  const [isFiltersInitialized, setIsFiltersInitialized] = useState(false);

  useGetAgreementsForActionsFilters(budgetValue, false);

  useEffect(() => {
    setIsFiltersInitialized(false);
  }, [budgetValue]);

  useEffect(() => {
    if (!isFiltersLoading && initialAgreementsFilters && !isFiltersInitialized) {
      setAgreementsFilters(initialAgreementsFilters);
      setIsFiltersInitialized(true);
    }
  }, [isFiltersLoading, initialAgreementsFilters, isFiltersInitialized]);

  useEffect(() => {
    if (agreementsComparisonModalData.length === 1
      && agreementsComparisonModalData[0]?.value?.id === currentAgreementId) {
      setBottomSectionSelected([currentAgreementId]);
      setTopSectionSelected([]);
    }
  }, [agreementsComparisonModalData.length, currentAgreementId]);

  useEffect(() => {
    setBottomSectionSelected((prevSelected) => {
      const normalizedCurrentId = String(currentAgreementId);
      const normalizedSelected = prevSelected.map((id) => String(id));

      if (!normalizedSelected.includes(normalizedCurrentId)) {
        return [currentAgreementId, ...prevSelected.filter(
          (id) => String(id) !== normalizedCurrentId,
        )];
      }
      return prevSelected;
    });
  }, [currentAgreementId]);

  const handleCalculationStatusChange = (_, value) => {
    setAgreementsFilters({
      ...agreementsFilters,
      calculation_statuses: value,
    });
  };

  const handleBudgetChange = (value) => {
    setBudgetValue(value);
  };

  const handleCheckedDown = () => {
    const agreementsToAdd = topSectionSelected
      .map((id) => availableAgreements.find((a) => a.id === id))
      .filter((agreement) => agreement && !agreementsComparisonModalData
        .some((a) => a.value?.id === agreement.id));

    const newAgreements = agreementsToAdd.map((agreement, index) => {
      const markerColor = agreementMarkersColors[index] || agreementMarkersColors[0] || '#6E6BCF';
      return {
        id: Date.now() + index,
        markerColor,
        value: {
          ...agreement,
          budget_name: budgets?.find((b) => b.id === budgetValue)?.name || 'Unknown Budget',
        },
        readOnly: false,
      };
    });

    setAgreementsComparisonModalData((prevData) => [...prevData, ...newAgreements]);

    agreementsToAdd.forEach(() => {
      removeColorFromAgreementMarkersColors();
    });

    setTopSectionSelected([]);
    setBottomSectionSelected((prevSelected) => [
      ...prevSelected,
      ...agreementsToAdd.map((a) => a.id),
    ]);
  };

  const handleCheckedTop = () => {
    const agreementsToRemove = bottomSectionSelected.filter(
      (id) => String(id) !== String(currentAgreementId),
    );

    const colorsToRestore = [];
    const newAgreementsComparisonModalData = agreementsComparisonModalData.filter(
      (agreement, idx) => {
        const shouldRemove = agreementsToRemove.some(
          (removeId) => String(agreement.value?.id) === String(removeId),
        ) && idx > 0;

        if (shouldRemove && agreement.markerColor) {
          colorsToRestore.push(agreement.markerColor);
        }

        return !shouldRemove;
      },
    );

    colorsToRestore.forEach((color) => {
      addColorToAgreementMarkersColors(color);
    });

    setAgreementsComparisonModalData(newAgreementsComparisonModalData);

    setBottomSectionSelected((prevSelected) => prevSelected.filter((id) => !agreementsToRemove.some(
      (removeId) => String(removeId) === String(id),
    )));

    setRefreshTrigger((prev) => prev + 1);
  };

  const handleAvailableSelectionChange = (newSelectedIds) => {
    setTopSectionSelected(newSelectedIds);
  };

  const handleComparisonSelectionChange = (newSelectedIds) => {
    const normalizedCurrentId = String(currentAgreementId);
    const normalizedNewSelectedIds = newSelectedIds.map((id) => String(id));

    let finalSelectedIds = [...newSelectedIds];
    if (!normalizedNewSelectedIds.includes(normalizedCurrentId)) {
      finalSelectedIds = [currentAgreementId, ...newSelectedIds];
    }

    setBottomSectionSelected(finalSelectedIds);
  };

  const AgreementCompareFilters = () => (
    <div className="budget-compare-filters-wrap">
      <BudgetSelection
        budgetValue={budgetValue}
        setBudgetValue={handleBudgetChange}
        isTitleVisible={false}
        isModalToCopyBudgetComponentsFromAnotherBudgetOpen
      />
      <Autocomplete
        className="calculation-status"
        name="calculation_statuses"
        autoComplete={false}
        selectOnFocus={false}
        limitTags={1}
        multiple
        options={calculationStatuses.map((s) => s.value)}
        value={agreementsFilters.calculation_statuses || []}
        onChange={handleCalculationStatusChange}
        getOptionLabel={(option) => {
          const found = calculationStatuses.find((item) => item.value === option);
          return found ? found.title : option;
        }}
        optionKey="title"
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            label="Calculation Status"
          />
        )}
      />
      <AgreementFilters
        isReadOnly={false}
        agreementsFilters={agreementsFilters}
        setAgreementsFilters={setAgreementsFilters}
      />
    </div>
  );

  const remainingSlots = 5 - agreementsComparisonModalData.length;
  const effectiveSelectionLimit = Math.max(0, remainingSlots);

  return (
    <div id="agreement-compare-filter" className="agreements-comparison-modal-content">
      <div className="agreements-comparison-modal-content__scrollable">
        <PerfectScrollbar options={{ suppressScrollX: true }} className="scrollbar-container">
          <AgreementsTableProvider
            budgetValue={budgetValue}
            agreementsFilters={agreementsFilters}
            selectedAgreementIds={topSectionSelected}
            setSelectedAgreementIds={handleAvailableSelectionChange}
            Actions={AgreementCompareFilters}
            columns={agreementsTableCompareConfig}
            excludedAgreementIds={comparisonAgreementIds}
            selectionLimit={effectiveSelectionLimit}
            refreshTrigger={refreshTrigger}
            isLoading={isInitialLoading}
            isFiltersInitialized={isFiltersInitialized}
          />

          <Box display="flex" justifyContent="start" alignItems="center" my={2}>
            <ActionButtonWithTooltip
              title="Add for comparison"
              disabled={topSectionSelected.length === 0
                || agreementsComparisonModalData.length >= 5
                || (topSectionSelected.length + agreementsComparisonModalData.length) > 5}
              icon={<GrDown fontSize="20" />}
              action={handleCheckedDown}
            />
            <ActionButtonWithTooltip
              title="Remove for comparison"
              disabled={bottomSectionSelected.filter(
                (id) => String(id) !== String(currentAgreementId),
              ).length === 0}
              icon={<GrUp fontSize="20" />}
              action={handleCheckedTop}
            />
          </Box>

          <div>
            <AgreementsTransferTable
              agreements={agreementsComparisonModalData.map((a) => a.value).filter(Boolean)}
              currentAgreementId={currentAgreementId}
              selectedAgreementIds={bottomSectionSelected}
              onSelectionChange={handleComparisonSelectionChange}
              agreementsWithMarkers={agreementsComparisonModalData}
            />
          </div>
        </PerfectScrollbar>
      </div>
      <FilterActionButtons
        confirmAction={confirmComparison}
        isCancelButtonVisible={false}
        resetAction={resetAgreementsComparison}
        resetButtonText="reset comparison"
        containerClassName={agreementsComparisonModalActionsModifier}
        disabled={getIsButtonConfirmDisabled(agreementsComparisonModalData)}
      />
    </div>
  );
};

AgreementsComparisonModalContent.propTypes = {
  resetAgreementsComparison: PropTypes.func.isRequired,
  confirmComparison: PropTypes.func.isRequired,
  agreementsComparisonModalData: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      name: PropTypes.string,
      budget_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      budget_name: PropTypes.string,
    }),
    markerColor: PropTypes.string,
  })).isRequired,
  setAgreementsComparisonModalData: PropTypes.func.isRequired,
  agreementMarkersColors: PropTypes.arrayOf(PropTypes.string).isRequired,
  removeColorFromAgreementMarkersColors: PropTypes.func.isRequired,
  addColorToAgreementMarkersColors: PropTypes.func.isRequired,
  currentAgreementId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default AgreementsComparisonModalContent;

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import BudgetListPreloader from '@nv2/nv2-pkg-js-shared-components/lib/ListPreloader';

import { HTTPService } from 'core/services';
import getBudgetsComponentsQuantityAction
  from 'pages/BudgetList/GetBudgetsComponentsQuantity/actions';
import useSavedBudgetsComparisonOnBudgetList from 'features/BudgetsComparisonFromBudgetList/hooks/useSavedBudgetsComparisonOnBudgetList';
import useBudgetListCalculationRefresh from './hooks/useBudgetListCalculationRefresh';

import getBudgetsAction from './GetBudgets/actions';
import BudgetListHeader from './BudgetListHeader';
import BudgetList from './BudgetList';
import getBudgetsKPITotalsAction from './GetBudgetsKPITotals/actions';
import initialBudgetsKPITotalsFilters from './BudgetsKPITotalsFilters/constants';
import BudgetListMainHeader from './BudgetListMainHeader';
import './BudgetListContainer.scss';

let budgetsController = HTTPService.getController();
let budgetsComponentsQuantityController = HTTPService.getController();

const BudgetListContainer = () => {
  const dispatch = useDispatch();
  const isBudgetsLoading = useSelector((state) => state.budgets.isLoading);
  const [budgetsKPITotalsFilters, setBudgetsKPITotalsFilters] = useState({
    start_date: initialBudgetsKPITotalsFilters.startDate,
    end_date: initialBudgetsKPITotalsFilters.endDate,
    currency_code: initialBudgetsKPITotalsFilters.currency.code,
    charge_type: initialBudgetsKPITotalsFilters.chargeType,
  });
  const [sortingKey, setSortingKey] = useState(null);
  const {
    budgetsSelectedStates,
    changeBudgetSelectedState,
    compareBudgets,
    comparedBudgets,
  } = useSavedBudgetsComparisonOnBudgetList();

  // Hook to refresh totals when budget calculations complete
  useBudgetListCalculationRefresh(budgetsKPITotalsFilters);

  const cancelGetBudgetsKPITotalsRequest = (controller) => {
    HTTPService.cancelRequest(controller);
  };

  const cancelGetBudgetsRequest = (controller) => {
    HTTPService.cancelRequest(controller);
  };

  const cancelGetBudgetsComponentsQuantityRequest = (controller) => {
    HTTPService.cancelRequest(controller);
  };

  const cancelBudgetsRequests = () => {
    cancelGetBudgetsRequest(budgetsController);
    cancelGetBudgetsComponentsQuantityRequest(budgetsComponentsQuantityController);
  };

  const updateBudgestControllers = () => {
    budgetsController = HTTPService.getController();
    budgetsComponentsQuantityController = HTTPService.getController();
  };

  const getBudgets = (key) => {
    const params = {
      sort_field: key,
    };

    dispatch(getBudgetsAction(budgetsController, params));
  };

  const getBudgetsComponentsQuantity = () => {
    dispatch(getBudgetsComponentsQuantityAction(budgetsComponentsQuantityController));
  };

  const getBudgetsKPITotals = (controller) => {
    const params = {
      ...budgetsKPITotalsFilters,
      charge_type: budgetsKPITotalsFilters.charge_type.value,
    };

    dispatch(getBudgetsKPITotalsAction(controller, params));
  };

  const setBudgetsData = (sortKey = null) => {
    cancelBudgetsRequests();

    updateBudgestControllers();

    getBudgets(sortKey);
    getBudgetsComponentsQuantity();
  };

  useEffect(() => {
    setBudgetsData();

    return () => cancelBudgetsRequests();
  }, []);

  useEffect(() => {
    const budgetsKPITotalsController = HTTPService.getController();

    getBudgetsKPITotals(budgetsKPITotalsController);

    return () => cancelGetBudgetsKPITotalsRequest(budgetsKPITotalsController);
  }, [budgetsKPITotalsFilters]);

  return (
    <div
      id="scrollableDiv"
      className="budget-list__scrolable"
    >
      <BudgetListMainHeader
        setBudgetsKPITotalsFilters={setBudgetsKPITotalsFilters}
        compareBudgets={compareBudgets}
        comparedBudgets={comparedBudgets}
        sortingKey={sortingKey}
        setSortingKey={setSortingKey}
        getBudgets={setBudgetsData}
      />
      <BudgetListHeader
        budgetsKPITotalsFilters={budgetsKPITotalsFilters}
      />
      {isBudgetsLoading
        ? <BudgetListPreloader />
        : (
          <BudgetList
            updateBudgetsData={setBudgetsData}
            budgetsSelectedStates={budgetsSelectedStates}
            changeBudgetSelectedState={changeBudgetSelectedState}
            currencyCode={budgetsKPITotalsFilters.currency_code}
            budgetsKPITotalsFilters={budgetsKPITotalsFilters}
          />
        )}
    </div>
  );
};

export default BudgetListContainer;

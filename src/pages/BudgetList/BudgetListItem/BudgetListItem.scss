.budget-list-item {
  margin: 0 20px 20px;
  border-radius: 4px;
  color: $dark-color-500 !important;

  &__link {
    text-decoration: none;
  }

  &__title {
    font-size: 16px !important;
    font-weight: 700 !important;
  }

  .info {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    @media (max-width: $small-desktop-width) {
      opacity: 1;
    }
  }

  &__action-btn {
    align-self: center;
    justify-self: center;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    display: flex;
    gap: 8px;
    align-items: center;

    @media (max-width: $small-desktop-width) {
      align-self: start;
      justify-self: end;
      opacity: 1;
    }

    @media (max-width: $mobile-width) {
      gap: 4px;
    }
  }

  &__wrap {
    height: 110px;
    padding: 5px 0 5px 5px;
    transition: all 0.3s ease-in-out;

    @media (max-width: $small-desktop-width) {
      padding: 15px;
    }

    &:hover {
      box-shadow: $shadow16;

      .budget-list-item__title {
        color: $brand-blue-color-500;
      }

      .info,
      .budget-list-item__action-btn {
        opacity: 1;
      }
    }

    @media (max-width: $small-desktop-width) {
      height: auto;
      padding: 15px;
      row-gap: 20px;
      grid-template-columns: 1fr 0.5fr 1fr 1.5fr 0.5fr;
      align-items: center;
      grid-template-areas:
        "title title type home-operators action"
        "divider divider divider divider divider"
        "period figures kpi kpi kpi";
    }

    @media (max-width: $mobile-width) {
      grid-template-columns: 0.5fr 0.5fr 0.5fr 1.5fr;
      grid-template-areas:
        "title title title action"
        "type home-operators home-operators period"
        "divider divider divider divider"
        "figures figures kpi kpi";
    }

    @media (max-width: $small-mobile-width) {
      grid-template-columns: 1fr 1fr 0.5fr;
      grid-template-areas:
        "title title action"
        "type home-operators home-operators"
        "period period period"
        "divider divider divider"
        "figures figures figures"
        "kpi kpi kpi";
    }
  }

  &__kpi-component {
    @media (max-width: $small-desktop-width) {
      margin: 0;
    }

    @media (max-width: $mobile-width) {
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 15px;
      margin-left: 0;
    }
  }

  &__budget-figures {
    &__item {
      background: $light-color-100;
      border-radius: 4px;
    }
  }

  &__kpi-totals-preloader {
    width: 92%;
    height: 40px !important;
    background-color: $light-color-100 !important;
    border-radius: 4px;
    margin-left: 5%;
  }

  .budget-list-item-template__kpi-component {
    .kpi-chart-bar {
      position: relative;
      top: 7px;
    }
  }

  .MuiCheckbox-root.MuiButtonBase-root {
    flex-shrink: 0 !important;
  }
}

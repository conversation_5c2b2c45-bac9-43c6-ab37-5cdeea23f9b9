import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import { Skeleton } from '@mui/material';
import { NavLink } from 'react-router-dom';
import styles
  from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import { getBudgetDetailsPath } from 'core/configs/paths';
import BudgetListItemTemplate from 'pages/BudgetList/BudgetListItemTemplate';
import useDeviceResolution from 'core/hooks/useDeviceResolution';
import BudgetKPIValuesBarChart from 'shared/BudgetKPIValuesBarChart';
import EntityTimeline from 'shared/EntityTimeline';
import FormattedOperators from 'shared/FormattedOperators';
import { useAppContext } from 'AppContextProvider';
import { BudgetDeletingButton, BudgetDeletingModal } from 'features/BudgetDeleting';
import BudgetCalculationButtonForBudgetList from 'features/BudgetCalculation/BudgetCalculationButtonForBudgetList';
import BudgetCalculationForBudgetList from 'features/BudgetCalculation/BudgetCalculationForBudgetList';
import useBudgetCalculationByBudgetId from 'features/BudgetCalculation/BudgetCalculationsByBudgetId/hooks/useBudgetCalculationByBudgetId';

import BudgetHeaderKpiComponent from 'pages/BudgetList/BudgetListHeader/BudgetHeaderKPIComponent';
import './BudgetListItem.scss';

const BudgetListItem = ({
  budgetsSelectedStates,
  changeBudgetSelectedState,
  budgetId,
  budgetName,
  budgetStartDate,
  budgetEndDate,
  budgetType,
  budgetLastHistoricalMonth,
  homeOperators,
  isMasterBudget,
  currencyCode,
  budgetKPIValuesDataMin,
  budgetKPIValuesDataMax,
  budgetsKPITotalsFilters,
  updateBudgetsData,
  budgetsCreatedAt,
  budgetsUpdatedAt,
  budgetsDescription,
}) => {
  const { getBrandColors } = useAppContext();

  const budgetsComponentsQuantityIsLoading = useSelector((
    state) => state.budgetsComponentsQuantity.isLoading);

  const budgetsComponentsQuantity = useSelector((state) => state.budgetsComponentsQuantity.data);

  const budgetsKPITotals = useSelector((state) => state.budgetsKPITotals.data);

  const budgetsKPITotalsIsLoading = useSelector((
    state) => state.budgetsKPITotals.isLoading);

  const { isSmallMobile } = useDeviceResolution();

  const inboundChartColor = getBrandColors(styles.greenColor500)[400];
  const outboundChartColor = getBrandColors(styles.redColor500)[400];

  const [isOpenDeletingModal, setIsOpenDeletingModal] = useState(false);

  const openDeletingModal = () => setIsOpenDeletingModal(true);
  const closeDeletingModal = () => setIsOpenDeletingModal(false);

  // Budget calculation state
  const { isCalculationStateIsRunning } = useBudgetCalculationByBudgetId(budgetId);
  const [triggerBudgetCalculationModal, setTriggerBudgetCalculationModal] = useState(0);

  const openCalculationModal = () => setTriggerBudgetCalculationModal((prev) => prev + 1);

  const budgetComponentsQuantity = budgetsComponentsQuantity.find(
    // eslint-disable-next-line camelcase
    ({ budget_id }) => budget_id === budgetId);

  const budgetFigures = [
    {
      data: !budgetComponentsQuantity
        ? ''
        : `${budgetComponentsQuantity?.active_agreements} / ${budgetComponentsQuantity?.agreements}`,
    },
    {
      data: budgetComponentsQuantity?.forecast_rules,
    },
  ];

  const KPIChart = budgetsKPITotalsIsLoading
    ? (
      <Skeleton
        variant="rect"
        className="budget-list-item__kpi-totals-preloader"
        data-testid="budget-list-item__kpi-totals-preloader"
        animation="wave"
      />
    )
    : (
      <>
        <BudgetKPIValuesBarChart
           /* eslint-disable-next-line camelcase */
          data={budgetsKPITotals.find(({ budget_id }) => budget_id === budgetId)?.total_charges}
          inboundColor={inboundChartColor}
          outboundColor={outboundChartColor}
          currencyUnit={currencyCode}
          dataMin={budgetKPIValuesDataMin}
          dataMax={budgetKPIValuesDataMax}
        />
        {isSmallMobile && (
        <BudgetHeaderKpiComponent
          chargeTypeTitle={budgetsKPITotalsFilters.charge_type.title}
          startDate={budgetsKPITotalsFilters.start_date}
          endDate={budgetsKPITotalsFilters.end_date}
          currencyCode={budgetsKPITotalsFilters.currency_code}
        />
        )}
      </>
    );

  const actionsButtons = !isMasterBudget ? (
    <>
      <BudgetDeletingButton onClick={openDeletingModal} />
      {isCalculationStateIsRunning && (
        <BudgetCalculationButtonForBudgetList
          budgetId={budgetId}
          onClick={openCalculationModal}
        />
      )}
    </>
  ) : undefined;

  const periodTimeline = (
    <EntityTimeline
      startDate={budgetStartDate}
      endDate={budgetEndDate}
      lastHistoricalMonth={budgetLastHistoricalMonth}
    />
  );

  const homeOperatorsWidget = (
    <FormattedOperators data={homeOperators} keyName="pmn_code" />
  );

  return (
    <>
      <NavLink
        to={getBudgetDetailsPath(budgetId)}
        key={`${budgetName.toString()}_${budgetId}`}
        className="budget-list-item__link"
      >
        <BudgetListItemTemplate
          period={periodTimeline}
          className="budget-list-item"
          title={budgetName}
          homeOperators={homeOperatorsWidget}
          type={budgetType}
          budgetFigures={budgetFigures}
          budgetFiguresIsLoading={budgetsComponentsQuantityIsLoading}
          isMaster={isMasterBudget}
          KPIComponent={KPIChart}
          isBudgetItem
          id={budgetId}
          checkedForComparison={budgetsSelectedStates?.[budgetId]?.state}
          onClickComparisonCheckbox={changeBudgetSelectedState}
          actionsButtons={actionsButtons}
          createdAt={budgetsCreatedAt}
          updatedAt={budgetsUpdatedAt}
          description={budgetsDescription}
        />
      </NavLink>
      <BudgetDeletingModal
        budgetId={budgetId}
        isOpenModal={isOpenDeletingModal}
        onCloseModal={closeDeletingModal}
        onSuccessDeleteAction={updateBudgetsData}
      />
      <BudgetCalculationForBudgetList
        budgetId={budgetId}
        onCalculationComplete={updateBudgetsData}
        triggerModal={triggerBudgetCalculationModal}
      />
    </>
  );
};

BudgetListItem.propTypes = {
  budgetsSelectedStates: PropTypes.instanceOf(Object),
  changeBudgetSelectedState: PropTypes.func,
  budgetId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]).isRequired,
  budgetName: PropTypes.string.isRequired,
  budgetStartDate: PropTypes.string.isRequired,
  budgetEndDate: PropTypes.string.isRequired,
  budgetType: PropTypes.oneOfType([PropTypes.string, PropTypes.element]).isRequired,
  budgetLastHistoricalMonth: PropTypes.string.isRequired,
  homeOperators: PropTypes.instanceOf(Object).isRequired,
  isMasterBudget: PropTypes.bool,
  currencyCode: PropTypes.string,
  budgetKPIValuesDataMin: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  budgetKPIValuesDataMax: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  budgetsKPITotalsFilters: PropTypes.instanceOf(Object),
  updateBudgetsData: PropTypes.func.isRequired,
  budgetsCreatedAt: PropTypes.string,
  budgetsUpdatedAt: PropTypes.string,
  budgetsDescription: PropTypes.string,
};

BudgetListItem.defaultProps = {
  budgetsSelectedStates: {},
  changeBudgetSelectedState: PropTypes.func,
  isMasterBudget: false,
  currencyCode: '',
  budgetKPIValuesDataMin: '',
  budgetKPIValuesDataMax: '',
  budgetsKPITotalsFilters: {},
  budgetsCreatedAt: '',
  budgetsUpdatedAt: '',
  budgetsDescription: '',
};

export default BudgetListItem;

import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { HTTPService } from 'core/services';
import getBudgetCalculationByIdAction from 'features/BudgetCalculation/BudgetCalculationsByBudgetId/actions';
import getBudgetsKPITotalsAction from '../GetBudgetsKPITotals/actions';

const useBudgetListCalculationRefresh = (budgetsKPITotalsFilters) => {
  const dispatch = useDispatch();
  const intervalRef = useRef(null);

  // Get all budgets to check their calculation status
  const budgets = useSelector((state) => state.budgets.data || []);
  const budgetCalculationsByBudgetId = useSelector((state) => (
    state.budgetCalculationsByBudgetId?.byBudgetId || {}
  ));

  const refreshBudgetsKPITotals = useCallback(() => {
    const controller = HTTPService.getController();

    const params = {
      ...budgetsKPITotalsFilters,
      charge_type: budgetsKPITotalsFilters.charge_type.value,
    };

    dispatch(getBudgetsKPITotalsAction(controller, params));
  }, [dispatch, budgetsKPITotalsFilters]);

  const checkBudgetCalculationStates = useCallback(() => {
    budgets.forEach((budget) => {
      const budgetId = budget.id;
      const currentState = budgetCalculationsByBudgetId[budgetId];

      // If we don't have state for this budget or it's been a while, fetch it
      if (!currentState || !currentState.data) {
        const controller = HTTPService.getController();
        dispatch(getBudgetCalculationByIdAction(controller, budgetId));
      }
    });
  }, [budgets, budgetCalculationsByBudgetId, dispatch]);

  useEffect(() => {
    // Initial check
    checkBudgetCalculationStates();

    // Set up polling every 30 seconds to check for calculation updates
    intervalRef.current = setInterval(() => {
      checkBudgetCalculationStates();
    }, 30000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [checkBudgetCalculationStates]);

  // Check if any calculation just finished and refresh totals
  useEffect(() => {
    let shouldRefresh = false;

    Object.values(budgetCalculationsByBudgetId).forEach((budgetState) => {
      const data = budgetState?.data;
      if (data?.status === 'CALCULATED') {
        shouldRefresh = true;
      }
    });

    if (shouldRefresh) {
      refreshBudgetsKPITotals();
    }
  }, [budgetCalculationsByBudgetId, refreshBudgetsKPITotals]);

  return {
    refreshBudgetsKPITotals,
    checkBudgetCalculationStates,
  };
};

export default useBudgetListCalculationRefresh;

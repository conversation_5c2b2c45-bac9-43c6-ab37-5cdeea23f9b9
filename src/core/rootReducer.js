import { combineReducers } from '@reduxjs/toolkit';
import toastrReducer from '@nv2/nv2-pkg-js-shared-components/lib/Toastr/reducer';

import getBudgetsReducer from 'pages/BudgetList/GetBudgets/reducer';

import appVariablesReducer from 'core/state/AppVariables/reducer';
import getBudgetParametersReducer
  from 'pages/BudgetDetails/BudgetParameters/GetBudgetParameters/reducer';
import getBudgetKPIValuesReducer
  from 'pages/BudgetDetails/BudgetValues/BudgetKPIValues/GetBudgetKPIValues/reducer';
import getCurrenciesReducer from 'features/CurrenciesAutocomplete/GetCurrencies/reducer';
import exportTrafficKPIValuesReducer from 'features/ExportTrafficKPIValues/reducer';
import getForecastRulesReducer
  from 'features/BudgetItems/ForecastRules/GetForecastRules/reducer';
import getForecastRulesFiltersReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/ForecastRulesTableFilters/GetForecastRulesFilters/reducer';
import updateForecastRuleReducer
  from 'features/BudgetItems/ForecastRules/UpdateForecastRule/reducer';
import createForecastRuleReducer
  from 'features/BudgetItems/ForecastRules/CreateForecastRule/reducer';
import getCountriesReducer from 'features/CountriesAutocomplete/GetCountries/reducer';
import getPredefinedFiltersReducer from 'features/GetPredefinedFilters/reducer';
import getCountryGroupsReducer from 'features/CountriyGroupsSelect/GetCountryGroups/reducer';
import getOperatorsReducer from 'features/OperatorsAutocomplete/GetOperators/reducer';
import removeForecastRulesReducer from 'features/BudgetItems/ForecastRules/RemoveForecastRules/reducer';
import removeAllForecastRulesReducer from 'features/BudgetItems/ForecastRules/RemoveAllForecastRules/reducer';
import getForecastRulesCoverageReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesCoverage/GetForecastRulesCoverage/reducer';
import getBudgetsComponentsQuantityReducer
  from 'pages/BudgetList/GetBudgetsComponentsQuantity/reducer';
import getBudgetsKPITotalsReducer
  from 'pages/BudgetList/GetBudgetsKPITotals/reducer';
import getBudgetCountriesValuesReducer
  from 'features/GetBudgetCountriesValues/reducer';
import getBudgetCountryOperatorsValuesReducer
  from 'features/GetBudgetCountryOperatorsValues/reducer';
import getAgreementsReducer
  from 'features/GetAgreements/reducer';
import getAgreementsFiltersReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementsTableFilters/GetAgreementsFilters/reducer';
import getAgreementParametersReducer
  from 'pages/AgreementDetails/AgreementParameters/GetAgreementParameters/reducer';
import getLinkedAgreementsReducer
  from 'pages/AgreementDetails/AgreementParameters/LinkedAgreements/GetLinkedAgreements/reducer';
import getDiscountsReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/GetDiscounts/reducer';
import getAgreementKPIValuesReducer
  from 'pages/AgreementDetails/AgreementValues/AgreementKPIValues/GetAgreementKPIValues/reducer';
import createBudgetReducer
  from 'pages/BudgetList/BudgetCreation/BudgetCreationModalContent/CreateBudget/reducer';
import createDefaultForecastRulesReducer
  from 'pages/BudgetList/BudgetCreation/BudgetCreationModalContent/CreateDefaultForecastRules/reducer';
import getBudgetComponentsQuantityReducer
  from 'features/BudgetItems/GetBudgetComponentsQuantity/reducer';
import getLastBudgetCalculationReducer
  from 'features/BudgetCalculation/GetLastBudgetCalculation/reducer';
import runBudgetCalculationReducer
  from 'features/BudgetCalculation/BudgetCalculationModal/BudgetCalculationRun/RunBudgetCalculation/reducer';
import budgetCalculationsByBudgetIdReducer
  from 'features/BudgetCalculation/BudgetCalculationsByBudgetId/reducer';
import getBudgetComponentsStateReducer
  from 'features/BudgetCalculation/BudgetCalculationModal/BudgetComponentsState/GetBudgetComponentsState/reducer';
import openBudgetDetailsChannelReducer from 'features/BudgetDetailsChannel/OpenBudgetDetailsChannel/reducer';
import getHomeOperatorsReducer from 'features/HomeOperatorsAutocomplete/GetHomeOperators/reducer';
import removeAgreementsReducer from 'features/RemoveAgreements/reducer';
import createAgreementReducer from 'pages/BudgetDetails/BudgetItems/Agreements/CreateAgreement/reducer';
import activateAgreementsReducer from 'features/ActivateAgreements/reducer';
import deactivateAgreementsReducer from 'features/DeactivateAgreements/reducer';
import copyAgreementsFromAnotherBudgetReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementTableActions/ModalToCopyAgreementsFromAnotherBudget/CopyAgreementsFromAnotherBudget/reducer';
import copyAllAgreementsFromAnotherBudgetReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementTableActions/ModalToCopyAgreementsFromAnotherBudget/CopyAllAgreementsFromAnotherBudget/reducer';
import agreementsForModalActionReducer from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsTable/GetAgreements/reducer';
import agreementsInModalToSubmitAgreementsToIotronReducer from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementsSubmissionToIotron/AgreementsSubmissionToIotronModal/AgreementsTable/GetAgreements/reducer';
import submitAgreementsToIotronReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementsSubmissionToIotron/AgreementsSubmissionToIotronModal/ModalActions/SubmitAgreementsToIotron/reducer';
import updateBudgetParametersReducer from 'pages/BudgetDetails/BudgetParameters/UpdateBudgetParameters/reducer';
import renewAgreementsReducer from 'features/RenewAgreements/reducer';

import createDiscountReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/DiscountCreation/DiscountCreationModalContent/CreateDiscount/reducer';
import getTrafficSegmentsReducer
  from 'features/GetTrafficSegments/reducer';
import editDiscountReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/DiscountEditing/DiscountEditingModalContent/EditDiscount/reducer';

import createSubDiscountReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/SubDiscountCreation/SubDiscountCreationModalContent/CreateSubDiscount/reducer';
import editSubDiscountReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/SubDiscountEditing/SubDiscountEditingModalContent/EditSubDiscount/reducer';
import deleteDiscountReducer
  from 'pages/AgreementDetails/AgreementItems/Discounts/DiscountDeleting/DeleteDiscount/reducer';
import getNegotiatorsReducer from 'features/NegotiatorsAutocomplete/GetNegotiators/reducer';
import updateAgreementParametersReducer
  from 'pages/AgreementDetails/AgreementParameters/UpdateAgreementParameters/reducer';
import getForecastRulesToCopyReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/ForecastRulesTableAction/ModalToCopyForecastRulesFromAnotherBudget/ForecastRulesTable/GetForecastRulesToCopy/reducer';
import copyForecastRulesFromAnotherBudgetReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/ForecastRulesTableAction/ModalToCopyForecastRulesFromAnotherBudget/CopyForecastRulesFromAnotherBudget/reducer';
import copyAllForecastRulesFromAnotherBudgetReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/ForecastRulesTableAction/ModalToCopyForecastRulesFromAnotherBudget/CopyAllForecastRulesFromAnotherBudget/reducer';
import exportBudgetReportingReducer from 'features/BudgetReportingDashboard/ExportBudgetReporting/reducer';
import getBudgetReportingReducer from 'features/BudgetReportingDashboard/GetBudgetReporting/reducer';
import getPartnerCountryOperatorSharesReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/hooks/useForecastRulesTableConfigs/useTableConfig/DistributionParameters/ManualDistributionParameters/shared/MarketShareModal/MarketShareModalContent/hooks/usePartnerCountryOparatorSharesData/GetPartnerCountryOperatorShares/reducer';
import getTrafficEvolutionReducer from 'features/TrafficEvolutionDashboard/GetTrafficEvolution/reducer';
import cloneAgreementReducer from 'features/CloneAgreement/reducer';
import saveComparedBudgetsFromBudgetListReducer
  from 'features/BudgetsComparisonFromBudgetList/SaveComparedBudgetsFormBudgetList/reducer';
import getClientCurrencyReducer from 'features/GetClientCurrency/reducer';
import getAgreementIntersectionsReducer from 'features/AgreementIntersectionsModal/GetAgreementIntersections/reducer';
import activateAllAgreementsReducer from 'pages/BudgetDetails/BudgetItems/Agreements/ActivateAllAgreements/reducer';
import deactivateAllAgreementsReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/DeactivateAllAgreements/reducer';
import getActiveBudgetBackgroundJobsReducer
  from 'features/BudgetDetailsChannel/BudgetDetailsChannelByBackgroundJobs/GetActiveBudgetBackgroundJobs/reducer';
import deleteBudgetReducer
  from 'features/BudgetDeleting/DeleteBudget/reducer';
import getForecastRulesToCopyFiltersReducer
  from 'features/BudgetItems/ForecastRules/ForecastRulesTable/ForecastRulesTableAction/ModalToCopyForecastRulesFromAnotherBudget/ForecastRulesToCopyFilters/GetForecastRulesToCopyFilters/reducer';

import exportAgreementsTableReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/ExportAgreementsTable/reducer';
import cloneSelectedAgreementsReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/CloneSelectedAgreements/reducer';
import getAgreementsForActionsFiltersReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/shared/AgreementsTableActions/AgreementsForActionsFilters/GetAgreementsToCopyFilters/reducer';
import changeAgreementsStatusReducer
  from 'pages/BudgetDetails/BudgetItems/Agreements/AgreementsTable/AgreementTableActions/ModalToChangeAgreementsStatus/ChangeAgreementsStatus/reducer';
import getIOTRatesReducer from 'pages/IOTRatesList/GetIOTRates/reducer';
import getIOTRatesFiltersReducer from 'pages/IOTRatesList/GetIOTRatesFilters/reducer';

const rootReducer = combineReducers({
  ...toastrReducer,
  appVariables: appVariablesReducer,
  budgets: getBudgetsReducer,
  createBudget: createBudgetReducer,
  createDefaultForecastRules: createDefaultForecastRulesReducer,
  budgetsComponentsQuantity: getBudgetsComponentsQuantityReducer,
  budgetComponentsQuantity: getBudgetComponentsQuantityReducer,
  budgetsKPITotals: getBudgetsKPITotalsReducer,
  budgetDetailsChannel: openBudgetDetailsChannelReducer,
  budgetParameters: getBudgetParametersReducer,
  activeBackgroundJobs: getActiveBudgetBackgroundJobsReducer,
  lastBudgetCalculation: getLastBudgetCalculationReducer,
  budgetCalculationRun: runBudgetCalculationReducer,
  budgetCalculationsByBudgetId: budgetCalculationsByBudgetIdReducer,
  budgetKPIValues: getBudgetKPIValuesReducer,
  budgetCountriesValues: getBudgetCountriesValuesReducer,
  budgetCountryOperatorsValues: getBudgetCountryOperatorsValuesReducer,
  trafficEvolution: getTrafficEvolutionReducer,
  budgetComponentsState: getBudgetComponentsStateReducer,
  currencies: getCurrenciesReducer,
  trafficKPIValuesXlsxFile: exportTrafficKPIValuesReducer,
  forecastRules: getForecastRulesReducer,
  forecastRulesFilters: getForecastRulesFiltersReducer,
  updatingForecastRule: updateForecastRuleReducer,
  creatingForecastRule: createForecastRuleReducer,
  removeForecastRules: removeForecastRulesReducer,
  removeAllForecastRules: removeAllForecastRulesReducer,
  partnerCountryOperatorShares: getPartnerCountryOperatorSharesReducer,
  countries: getCountriesReducer,
  countryGroups: getCountryGroupsReducer,
  operators: getOperatorsReducer,
  homeOperators: getHomeOperatorsReducer,
  predefinedFilters: getPredefinedFiltersReducer,
  forecastRulesCoverage: getForecastRulesCoverageReducer,
  agreements: getAgreementsReducer,
  agreementsFilters: getAgreementsFiltersReducer,
  agreementsForModalAction: agreementsForModalActionReducer,
  agreementsInModalToSubmitAgreementsToIotron:
  agreementsInModalToSubmitAgreementsToIotronReducer,
  submitAgreementsToIotron: submitAgreementsToIotronReducer,
  agreementParameters: getAgreementParametersReducer,
  linkedAgreements: getLinkedAgreementsReducer,
  agreementKPIValues: getAgreementKPIValuesReducer,
  discounts: getDiscountsReducer,
  createDiscount: createDiscountReducer,
  createSubDiscount: createSubDiscountReducer,
  editDiscount: editDiscountReducer,
  editSubDiscount: editSubDiscountReducer,
  deleteDiscount: deleteDiscountReducer,
  trafficSegments: getTrafficSegmentsReducer,
  removeAgreements: removeAgreementsReducer,
  activateAgreements: activateAgreementsReducer,
  createAgreement: createAgreementReducer,
  deactivateAgreements: deactivateAgreementsReducer,
  copyAgreementsFromAnotherBudget: copyAgreementsFromAnotherBudgetReducer,
  copyAllAgreementsFromAnotherBudget: copyAllAgreementsFromAnotherBudgetReducer,
  renewAgreements: renewAgreementsReducer,
  cloneAgreement: cloneAgreementReducer,
  updatingBudgetParameters: updateBudgetParametersReducer,
  budgetReporting: getBudgetReportingReducer,
  budgetReportingXlsxFile: exportBudgetReportingReducer,
  negotiators: getNegotiatorsReducer,
  updatingAgreementParameters: updateAgreementParametersReducer,
  forecastRulesInModalToCopyForecastRulesFromAnotherBudget:
  getForecastRulesToCopyReducer,
  copyForecastRulesFromAnotherBudget: copyForecastRulesFromAnotherBudgetReducer,
  copyAllForecastRulesFromAnotherBudget: copyAllForecastRulesFromAnotherBudgetReducer,
  saveComparedBudgetsFromBudgetList: saveComparedBudgetsFromBudgetListReducer,
  clientCurrency: getClientCurrencyReducer,
  agreementIntersections: getAgreementIntersectionsReducer,
  activateAllAgreements: activateAllAgreementsReducer,
  deactivateAllAgreements: deactivateAllAgreementsReducer,
  deleteBudget: deleteBudgetReducer,
  forecastRulesToCopyFilters: getForecastRulesToCopyFiltersReducer,
  agreementsForActionsFilters: getAgreementsForActionsFiltersReducer,
  exportAgreementsTable: exportAgreementsTableReducer,
  cloneSelectedAgreements: cloneSelectedAgreementsReducer,
  iotRates: getIOTRatesReducer,
  iotRatesFilters: getIOTRatesFiltersReducer,
  changeAgreementsStatus: changeAgreementsStatusReducer,
});

export default rootReducer;
